import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Book, Plus, Settings, LogOut, User, BarChart3, Brain, MessageSquare } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserProfile } from '@/hooks/useUserProfile';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/utils/utils';

export const Navigation = () => {
  const { user, signOut } = useAuth();
  const { profile } = useUserProfile();
  const location = useLocation();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      console.log('🧭 Navigation: Initiating sign-out');
      await signOut();
    } catch (error) {
      console.error('🧭 Navigation: Error signing out:', error);
      // AuthContext will handle redirect even on error
    }
  };

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  // Helper function to check if a route is active
  const isActiveRoute = (path: string): boolean => {
    return location.pathname === path;
  };

  return (
    <nav className="sticky top-0 z-50 backdrop-blur-md bg-white/30 border-b border-amber-200/30 shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between gap-4">
          <Link to="/" className="flex items-center flex-shrink-0">
            <h1 className="text-2xl font-bold text-gradient">Amberglow</h1>
          </Link>

          <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto flex-shrink-0">
            {user ? (
              <>
                <Link to="/journal">
                  <Button
                    variant={isActiveRoute('/journal') ? 'default' : 'ghost'}
                    size="sm"
                    className={cn(
                      'flex-shrink-0',
                      isActiveRoute('/journal')
                        ? 'bg-amber-500 hover:bg-amber-600 text-white'
                        : 'hover:bg-amber-50 text-amber-700'
                    )}
                  >
                    <Book className="w-4 h-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Journal</span>
                  </Button>
                </Link>
                <Link to="/write">
                  <Button
                    variant={isActiveRoute('/write') ? 'default' : 'ghost'}
                    size="sm"
                    className={cn(
                      'flex-shrink-0',
                      isActiveRoute('/write')
                        ? 'bg-amber-500 hover:bg-amber-600 text-white'
                        : 'hover:bg-amber-50 text-amber-700'
                    )}
                  >
                    <Plus className="w-4 h-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Write</span>
                  </Button>
                </Link>
                <Link to="/analytics">
                  <Button
                    variant={isActiveRoute('/analytics') ? 'default' : 'ghost'}
                    size="sm"
                    className={cn(
                      'flex-shrink-0',
                      isActiveRoute('/analytics')
                        ? 'bg-amber-500 hover:bg-amber-600 text-white'
                        : 'hover:bg-amber-50 text-amber-700'
                    )}
                  >
                    <BarChart3 className="w-4 h-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Analytics</span>
                  </Button>
                </Link>
                <Link to="/journal-chat">
                  <Button
                    variant={isActiveRoute('/journal-chat') ? 'default' : 'ghost'}
                    size="sm"
                    className={cn(
                      'flex-shrink-0',
                      isActiveRoute('/journal-chat')
                        ? 'bg-amber-500 hover:bg-amber-600 text-white'
                        : 'hover:bg-amber-50 text-amber-700'
                    )}
                  >
                    <MessageSquare className="w-4 h-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Chat</span>
                  </Button>
                </Link>
                <Link to="/settings#memories">
                  <Button
                    variant={isActiveRoute('/settings') ? 'default' : 'ghost'}
                    size="sm"
                    className={cn(
                      'flex-shrink-0',
                      isActiveRoute('/settings')
                        ? 'bg-amber-500 hover:bg-amber-600 text-white'
                        : 'hover:bg-amber-50 text-amber-700'
                    )}
                  >
                    <Brain className="w-4 h-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Memories</span>
                  </Button>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full flex-shrink-0 ml-1">
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={profile?.avatar_url || user.user_metadata?.avatar_url}
                          alt={profile?.full_name || user.email}
                        />
                        <AvatarFallback>
                          {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <div className="flex items-center justify-start gap-2 p-2">
                      <div className="flex flex-col space-y-1 leading-none">
                        {profile?.full_name && (
                          <p className="font-medium">{profile.full_name}</p>
                        )}
                        <p className="w-[200px] truncate text-sm text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => navigate('/analytics')}>
                      <BarChart3 className="mr-2 h-4 w-4" />
                      <span>Analytics</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSettingsClick}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSignOut}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <Link to="/auth">
                <Button className="bg-amber-500 hover:bg-amber-600 text-white">
                  <User className="w-4 h-4 mr-2" />
                  Sign In
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

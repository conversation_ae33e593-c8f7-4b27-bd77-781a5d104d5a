import { But<PERSON> } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Star, Book, Brain, Shield, TrendingUp, Sparkles, Users, Award } from 'lucide-react';
import { <PERSON> } from '@/components/layout/Hero';
import PageLayout from '@/components/layout/PageLayout';

import { useNavigation } from '@/hooks/useNavigation';


const Index = () => {
  const { handleGetStarted } = useNavigation();

  console.log('🏠 Index render: Home page');

  return (
    <PageLayout fullHeight>
      <Hero onGetStarted={handleGetStarted} />

      {/* Features Section */}
      <section className="py-24 px-6 bg-white/50 backdrop-blur-sm" aria-labelledby="features-heading">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100 rounded-full text-amber-700 text-sm font-medium mb-6">
              <Sparkles className="w-4 h-4" />
              Powerful Features
            </div>
            <h2 id="features-heading" className="text-4xl md:text-5xl font-bold text-gradient mb-6 leading-tight px-4 py-2">
              Everything you need for mindful growth
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              AmberGlow combines the power of journaling with AI insights to create a transformative experience for your mental wellness journey
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-white to-amber-50/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="text-center p-8 relative z-10">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-400 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <Book className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-2xl mb-4 text-gray-800">Daily Journal</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                  Express your thoughts and feelings in a beautiful, distraction-free writing environment designed for mindful reflection
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-white to-orange-50/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-amber-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="text-center p-8 relative z-10">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-amber-400 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <Brain className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-2xl mb-4 text-gray-800">AI Insights</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                  Get personalized reflections and emotional insights powered by advanced AI to deepen your self-understanding
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-white to-purple-50/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-amber-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="text-center p-8 relative z-10">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-400 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <TrendingUp className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-2xl mb-4 text-gray-800">Growth Tracking</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                  Visualize your emotional patterns and personal growth journey with meaningful analytics and progress insights
                </CardDescription>
              </CardHeader>
            </Card>
          </div>

          {/* Additional features grid */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="flex items-center gap-4 p-6 bg-white/60 rounded-xl backdrop-blur-sm border border-amber-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">Private & Secure</h3>
                <p className="text-sm text-gray-600">Your thoughts stay safe with enterprise-grade security</p>
              </div>
            </div>

            <div className="flex items-center gap-4 p-6 bg-white/60 rounded-xl backdrop-blur-sm border border-amber-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">Community Support</h3>
                <p className="text-sm text-gray-600">Connect with like-minded individuals on similar journeys</p>
              </div>
            </div>

            <div className="flex items-center gap-4 p-6 bg-white/60 rounded-xl backdrop-blur-sm border border-amber-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Award className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">Expert Guidance</h3>
                <p className="text-sm text-gray-600">Built with insights from mental health professionals</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-6 bg-gradient-to-br from-amber-500 to-orange-500 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/5"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-white rounded-full blur-2xl"></div>
        </div>

        <div className="container mx-auto text-center max-w-4xl relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            Ready to transform your inner world?
          </h2>
          <p className="text-xl text-white/90 mb-10 leading-relaxed max-w-2xl mx-auto">
            Join thousands of people who have discovered the power of mindful journaling with AI-powered insights
          </p>
          <div className="flex justify-center">
            <Button
              size="lg"
              onClick={handleGetStarted}
              className="bg-white text-amber-600 hover:bg-gray-50 px-12 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <Book className="w-5 h-5 mr-2" />
              Start Your Journey
            </Button>
          </div>

          <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-white/80 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              Free to start
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              No credit card required
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              Cancel anytime
            </div>
          </div>
        </div>
      </section>
    </PageLayout>
  );
};

export default Index;

/**
 * Performance Optimization Utilities
 * Helper functions and HOCs for React component performance optimization
 */

import React, { memo, useMemo, useCallback, lazy, Suspense, ComponentType } from 'react';
import { ComponentPerformanceConfig, OptimizationStrategy } from '@/types/performance';

/**
 * Enhanced memo with deep comparison option
 */
export function deepMemo<T extends ComponentType<any>>(
  Component: T,
  areEqual?: (prevProps: any, nextProps: any) => boolean
): T {
  const defaultAreEqual = (prevProps: any, nextProps: any) => {
    return JSON.stringify(prevProps) === JSON.stringify(nextProps);
  };

  return memo(Component, areEqual || defaultAreEqual) as T;
}

/**
 * Memoization hook with dependency tracking
 */
export function useStableMemo<T>(
  factory: () => T,
  deps: React.DependencyList,
  debugName?: string
): T {
  return useMemo(() => {
    const startTime = performance.now();
    const result = factory();
    const endTime = performance.now();

    if (debugName && process.env.NODE_ENV === 'development') {
      console.log(`[${debugName}] Memo computation took ${endTime - startTime}ms`);
    }

    return result;
  }, deps);
}

/**
 * Stable callback hook with performance tracking
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList,
  debugName?: string
): T {
  return useCallback((...args: any[]) => {
    const startTime = performance.now();
    const result = callback(...args);
    const endTime = performance.now();

    if (debugName && process.env.NODE_ENV === 'development') {
      console.log(`[${debugName}] Callback execution took ${endTime - startTime}ms`);
    }

    return result;
  }, deps) as T;
}

/**
 * Debounced callback hook
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList
): T {
  const debouncedCallback = useCallback(debounce(callback, delay), [callback, delay, ...deps]);

  return debouncedCallback as T;
}

/**
 * Throttled callback hook
 */
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList
): T {
  const throttledCallback = useCallback(throttle(callback, delay), [callback, delay, ...deps]);

  return throttledCallback as T;
}

/**
 * Debounce function
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Throttle function
 */
function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;

  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

/**
 * Lazy component loader with error boundary
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType,
  errorFallback?: React.ComponentType<{ error: Error }>
): React.ComponentType {
  const LazyComponent = lazy(importFunc);

  const FallbackComponent =
    fallback ||
    (() => (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    ));

  const ErrorFallback =
    errorFallback ||
    (({ error }: { error: Error }) => (
      <div className="flex items-center justify-center p-4 text-red-600">
        <p>Failed to load component: {error.message}</p>
      </div>
    ));

  return (props: any) => (
    <ErrorBoundary fallback={ErrorFallback}>
      <Suspense fallback={<FallbackComponent />}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Error boundary component
 */
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback;
      return <FallbackComponent error={this.state.error} />;
    }

    return this.props.children;
  }
}

/**
 * Performance monitoring HOC
 */
export function withPerformanceMonitoring<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
): ComponentType<P> {
  const PerformanceMonitoredComponent = (props: P) => {
    const renderStartTime = performance.now();

    React.useEffect(() => {
      const renderEndTime = performance.now();
      const renderTime = renderEndTime - renderStartTime;

      if (import.meta.env.DEV) {
        console.log(`[${componentName || Component.name}] Render time: ${renderTime}ms`);

        // Log warning for slow renders
        if (renderTime > 16) {
          // 60fps threshold
          console.warn(
            `[${componentName || Component.name}] Slow render detected: ${renderTime}ms`
          );
        }
      }
    });

    return React.createElement(Component, props);
  };

  PerformanceMonitoredComponent.displayName = `withPerformanceMonitoring(${componentName || Component.name})`;

  return PerformanceMonitoredComponent;
}

/**
 * Virtualization helper for large lists
 */
export function useVirtualization(
  items: any[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = React.useState(0);

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );

    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
    visibleRange,
  };
}

/**
 * Intersection observer hook for lazy loading
 */
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
): [React.RefObject<HTMLElement>, boolean] {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const ref = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [JSON.stringify(options)]); // Use JSON.stringify to avoid object reference issues

  return [ref, isIntersecting];
}

/**
 * Performance optimization HOC factory
 */
export function createOptimizedComponent<P extends object>(
  Component: ComponentType<P>,
  config: ComponentPerformanceConfig
): ComponentType<P> {
  let OptimizedComponent = Component;

  // Apply memoization
  if (config.strategies.includes('memoization')) {
    OptimizedComponent = memo(OptimizedComponent);
  }

  // Apply performance monitoring
  if (import.meta.env.DEV) {
    OptimizedComponent = withPerformanceMonitoring(OptimizedComponent);
  }

  return OptimizedComponent;
}

/**
 * Bundle size analyzer (development only)
 */
export function analyzeBundleSize(componentName: string) {
  if (!import.meta.env.DEV) return;

  const startTime = performance.now();

  return {
    end: () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;

      console.log(`[Bundle Analysis] ${componentName} load time: ${loadTime}ms`);

      // Estimate bundle size impact
      if (loadTime > 100) {
        console.warn(`[Bundle Analysis] ${componentName} may have a large bundle impact`);
      }
    },
  };
}

/**
 * Memory usage tracker
 */
export function useMemoryUsage(componentName?: string) {
  React.useEffect(() => {
    if (!import.meta.env.DEV) return;

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        console.log(
          `[Memory] ${componentName || 'Component'} - Used: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`
        );
      }
    };

    checkMemory();

    return () => {
      checkMemory();
    };
  }, [componentName]);
}

/**
 * Render count tracker (development only)
 */
export function useRenderCount(componentName?: string) {
  const renderCount = React.useRef(0);

  React.useEffect(() => {
    renderCount.current += 1;

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Render Count] ${componentName || 'Component'}: ${renderCount.current}`);

      // Warn about excessive re-renders
      if (renderCount.current > 10) {
        console.warn(
          `[Render Count] ${componentName || 'Component'} has rendered ${renderCount.current} times`
        );
      }
    }
  });

  return renderCount.current;
}

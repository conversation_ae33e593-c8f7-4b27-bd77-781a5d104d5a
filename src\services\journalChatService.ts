/**
 * Journal Chat Service
 * Service for generating chat responses based on user's journal entries
 * Uses RAG (Retrieval Augmented Generation) approach with semantic embeddings
 */

import { supabase } from '@/integrations/supabase/client';
import { ApiResponse, JournalEntry } from '@/types';
import { generateEmbedding, calculateCosineSimilarity } from './embeddingService';
import { formatJournalEntryForDisplay } from './journalService';
import { generateAIResponse } from './journalChatAIService';
import { JournalChatContext, JournalChatResponse } from '@/types/journal-chat';

// Maximum number of journal entries to retrieve as context
const MAX_CONTEXT_ENTRIES = 10;

// Minimum relevance score for entries to be considered relevant (0-1)
const MIN_RELEVANCE_THRESHOLD = 0.2;

/**
 * Retrieve journal entries relevant to the query
 * Uses semantic similarity via embeddings
 */
export const getRelevantJournalEntries = async (
  query: string,
  limit: number = MAX_CONTEXT_ENTRIES
): Promise<ApiResponse<JournalEntry[]>> => {
  try {
    console.log('🔍 Finding relevant journal entries for:', query);
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    // Generate embedding for the query
    const queryEmbeddingResult = await generateEmbedding(query);
    if (!queryEmbeddingResult.success) {
      console.error('Failed to generate embedding for query:', queryEmbeddingResult.error);
      return {
        success: false,
        error: { message: 'Failed to generate embedding for query', code: 'EMBEDDING_ERROR' }
      };
    }

    const queryEmbedding = queryEmbeddingResult.data;

    // Get all journal entries for the user
    const { data: entries, error } = await supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error || !entries) {
      console.error('Error fetching journal entries:', error);
      return {
        success: false,
        error: { message: 'Failed to fetch journal entries', code: 'DATABASE_ERROR' }
      };
    }

    if (entries.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // Calculate similarity scores for each entry
    // Cast database entries to JournalEntry type
    const journalEntries = entries as unknown as JournalEntry[];
    
    const entriesWithScores = await Promise.all(journalEntries.map(async (entry) => {
      // Create text representation for embedding
      const entryText = `${entry.title} ${entry.content} ${entry.ai_summary || ''}`;
      
      // Try to use existing embedding if available
      let similarity = 0;

      try {
        // Generate embedding for the entry
        const entryEmbeddingResult = await generateEmbedding(entryText);
        if (entryEmbeddingResult.success) {
          // Calculate cosine similarity
          if (queryEmbedding && entryEmbeddingResult.data) {
            similarity = calculateCosineSimilarity(queryEmbedding, entryEmbeddingResult.data);
          } else {
            // Fallback to keyword matching if embedding fails
            const normalizedQuery = query.toLowerCase();
            const normalizedEntry = entryText.toLowerCase();
            
            // Simple keyword matching
            const queryWords = normalizedQuery.split(/\s+/).filter(w => w.length > 2);
            let matches = 0;
            
            for (const word of queryWords) {
              if (normalizedEntry.includes(word)) {
                matches++;
              }
            }
            
            similarity = queryWords.length > 0 ? matches / queryWords.length : 0;
          }
        } else {
          // Fallback to keyword matching if embedding fails
          const normalizedQuery = query.toLowerCase();
          const normalizedEntry = entryText.toLowerCase();
          
          // Simple keyword matching
          const queryWords = normalizedQuery.split(/\s+/).filter(w => w.length > 2);
          let matches = 0;
          
          for (const word of queryWords) {
            if (normalizedEntry.includes(word)) {
              matches++;
            }
          }
          
          similarity = queryWords.length > 0 ? matches / queryWords.length : 0;
        }
      } catch (error) {
        console.error('Error calculating similarity for entry:', error);
      }

      return {
        entry,
        similarity,
      };
    }));

    // Filter by minimum relevance and sort by similarity
    const relevantEntries = entriesWithScores
      .filter(({ similarity }) => similarity >= MIN_RELEVANCE_THRESHOLD)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(({ entry }) => entry);

    console.log(`Found ${relevantEntries.length} relevant entries out of ${entries.length} total`);

    return {
      success: true,
      data: relevantEntries,
    };
  } catch (error) {
    console.error('Error retrieving relevant journal entries:', error);
    return {
      success: false,
      error: { message: 'Failed to retrieve relevant journal entries', code: 'PROCESSING_ERROR' }
    };
  }
};

/**
 * Generate a response from Amber based on journal entries
 */
export const generateJournalChatResponse = async (
  query: string
): Promise<ApiResponse<JournalChatResponse>> => {
  try {
    // Get relevant journal entries
    const entriesResult = await getRelevantJournalEntries(query);
    if (!entriesResult.success || !entriesResult.data) {
      return {
        success: false,
        error: entriesResult.error || { message: 'Failed to retrieve journal entries', code: 'DATA_RETRIEVAL_ERROR' }
      };
    }

    const entries = entriesResult.data;
    
    // Format entries as context
    const entriesForContext = entries.map(entry => {
      const formatted = formatJournalEntryForDisplay(entry);
      return {
        id: entry.id,
        date: formatted.date,
        title: formatted.title,
        content: formatted.content,
        summary: formatted.ai_summary || undefined,
        relevance: 1.0 // We'll improve this later
      };
    });

    // If no relevant entries found
    if (entries.length === 0) {
      return {
        success: true,
        data: {
          response: "I don't see any journal entries related to that topic yet. Would you like to write about it in your journal?",
          context: { entries: [] }
        }
      };
    }
    
    // Generate AI response using journal entries as context
    const aiResponse = await generateAIResponse(query, { entries: entriesForContext });
    
    // If AI response generation succeeds, return it
    if (aiResponse.success && aiResponse.data) {
      return aiResponse;
    }
    
    // If AI response generation fails, log the error and provide a fallback response
    console.error('AI response generation failed:', aiResponse.error);
    
    // Basic fallback response when AI fails
    let response = "I'm having trouble analyzing your journal entries right now. ";
    
    if (entries.length > 0) {
      response += "But I can see you have some related entries. Would you like to explore them together?";
    } else {
      response += "I don't see any journal entries directly related to your question. Would you like to tell me more?";
    }

    return {
      success: true,
      data: {
        response,
        context: { entries: entriesForContext }
      }
    };
  } catch (error) {
    console.error('Error generating journal chat response:', error);
    return {
      success: false,
      error: { message: 'Failed to generate response', code: 'GENERATION_ERROR' }
    };
  }
};

// No longer needed - AI service handles this

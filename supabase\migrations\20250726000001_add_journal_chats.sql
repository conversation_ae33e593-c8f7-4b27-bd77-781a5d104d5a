-- Migration: Add Journal Chat Tables
-- Creates tables for storing journal chat conversations and messages

-- Table for storing journal chat conversations
CREATE TABLE IF NOT EXISTS journal_chat_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_pinned BOOLEAN NOT NULL DEFAULT FALSE,
  context_size INT NOT NULL DEFAULT 10, -- Number of journal entries to include as context
  
  -- Add RLS policies
  CONSTRAINT journal_chat_conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Table for storing journal chat messages
CREATE TABLE IF NOT EXISTS journal_chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES journal_chat_conversations(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Optional fields
  context JSONB, -- Stores the relevant journal entries used for context
  is_error BOOLEAN DEFAULT FALSE,
  embedding VECTOR(384) -- For semantic search functionality
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS journal_chat_conversations_user_id_idx ON journal_chat_conversations (user_id);
CREATE INDEX IF NOT EXISTS journal_chat_messages_conversation_id_idx ON journal_chat_messages (conversation_id);
CREATE INDEX IF NOT EXISTS journal_chat_messages_created_at_idx ON journal_chat_messages (created_at);

-- Create function to update updated_at on conversation updates
CREATE OR REPLACE FUNCTION update_journal_chat_conversation_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating conversation timestamp
CREATE TRIGGER journal_chat_conversation_updated
BEFORE UPDATE ON journal_chat_conversations
FOR EACH ROW
EXECUTE FUNCTION update_journal_chat_conversation_timestamp();

-- Row Level Security
-- Enable RLS
ALTER TABLE journal_chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_chat_messages ENABLE ROW LEVEL SECURITY;

-- Create policies for conversations
CREATE POLICY journal_chat_conversations_select ON journal_chat_conversations 
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY journal_chat_conversations_insert ON journal_chat_conversations 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY journal_chat_conversations_update ON journal_chat_conversations 
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY journal_chat_conversations_delete ON journal_chat_conversations 
  FOR DELETE USING (auth.uid() = user_id);

-- Create policies for messages (through conversation access)
CREATE POLICY journal_chat_messages_select ON journal_chat_messages 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM journal_chat_conversations 
      WHERE id = journal_chat_messages.conversation_id 
      AND user_id = auth.uid()
    )
  );

CREATE POLICY journal_chat_messages_insert ON journal_chat_messages 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM journal_chat_conversations 
      WHERE id = journal_chat_messages.conversation_id 
      AND user_id = auth.uid()
    )
  );

CREATE POLICY journal_chat_messages_update ON journal_chat_messages 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM journal_chat_conversations 
      WHERE id = journal_chat_messages.conversation_id 
      AND user_id = auth.uid()
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1 FROM journal_chat_conversations 
      WHERE id = journal_chat_messages.conversation_id 
      AND user_id = auth.uid()
    )
  );

CREATE POLICY journal_chat_messages_delete ON journal_chat_messages 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM journal_chat_conversations 
      WHERE id = journal_chat_messages.conversation_id 
      AND user_id = auth.uid()
    )
  );

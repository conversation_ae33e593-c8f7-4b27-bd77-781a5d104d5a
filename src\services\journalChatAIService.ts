/**
 * Journal Chat AI Service
 * Service for generating AI responses for the Journal Chat feature
 * Uses Gemini AI model to generate insightful responses based on journal entries
 */

import { ApiResponse } from '@/types';
import { JournalChatResponse, JournalChatContext } from '@/types/journal-chat';
import { JournalEntry } from '@/types';
import { utcTimestampToLocalDateString } from '@/utils/dateUtils';

// Configuration - Use Vite environment variables
const DEFAULT_API_KEY = import.meta.env.VITE_GEMINI_API_KEY || null;

/**
 * Get configuration
 */
const getConfig = () => ({
  geminiApiKey: localStorage.getItem('gemini_api_key') || DEFAULT_API_KEY
});

// Maximum number of entries to include in context
const MAX_CONTEXT_ENTRIES = 10; // Used in generateAIResponse

/**
 * Format journal entries for prompt context
 */
export const formatEntriesForPrompt = (entries: JournalEntry[]): string => {
  return entries.map((entry) => {
    const date = utcTimestampToLocalDateString(entry.created_at);
    const summary = entry.ai_summary || '';
    const emotion = entry.emotion || '';
    const moodScore = entry.mood_score ? `${entry.mood_score}/10` : '';
    
    return `Date: ${date}
Title: ${entry.title}
${emotion ? `Emotion: ${emotion}` : ''}${moodScore ? ` (Mood: ${moodScore})` : ''}
${summary ? `Summary: ${summary}\n` : ''}Content: ${entry.content.slice(0, 300)}${entry.content.length > 300 ? '...' : ''}
---`;
  }).join('\n\n');
};

/**
 * Generate AI response using Gemini
 */
export const generateAIResponse = async (
  query: string,
  context: JournalChatContext
): Promise<ApiResponse<JournalChatResponse>> => {
  try {
    const config = getConfig();
    const apiKey = config.geminiApiKey;
    
    if (!apiKey) {
      console.warn('Gemini API key not found, falling back to simulated response');
      return generateSimulatedResponse(query, context);
    }
    
    // Build the prompt with the user's query and journal context - limit to max entries
    const limitedEntries = context.entries.slice(0, MAX_CONTEXT_ENTRIES);
    const entriesText = limitedEntries.length > 0 
      ? limitedEntries.map(entry => `Date: ${entry.date}\nTitle: ${entry.title}\nContent: ${entry.content}`).join('\n\n')
      : 'No relevant journal entries found.';
    
    const prompt = `You are Amber, a friendly and empathetic journal assistant.
The user asked: "${query}".

Based on their journal entries below, respond with helpful, thoughtful, and reflective insight.
Be conversational and warm. If relevant, include specific references to the journal content.
If there's not enough information in their journal to answer well, acknowledge this politely.

Journal Entries:
${entriesText}

Your response:`;

    // Call Gemini API
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': apiKey,
      },
      body: JSON.stringify({
        contents: [{
          role: 'user',
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 800,
        },
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini API error:', errorText);
      return {
        success: false,
        error: { 
          message: `AI service error: ${response.status} ${response.statusText}`,
          code: 'AI_SERVICE_ERROR'
        }
      };
    }

    const responseData = await response.json();
    
    // Extract the text from the response
    const aiResponse = responseData?.candidates?.[0]?.content?.parts?.[0]?.text || '';
    
    if (!aiResponse) {
      console.error('Empty response from Gemini API:', responseData);
      return {
        success: false,
        error: { 
          message: 'Empty response from AI service',
          code: 'EMPTY_RESPONSE'
        }
      };
    }

    // Return the AI-generated response
    return {
      success: true,
      data: {
        response: aiResponse,
        context: context
      }
    };
  } catch (error) {
    console.error('Error generating AI response:', error);
    
    // Fall back to simulated response on error
    console.log('Falling back to simulated response due to error');
    return generateSimulatedResponse(query, context);
  }
};

/**
 * Generate a simulated response when the AI service is unavailable
 * This is a fallback mechanism for when the API is down or rate limited
 */
export const generateSimulatedResponse = async (
  _query: string, // Prefixed with underscore to indicate it's not used
  context: JournalChatContext
): Promise<ApiResponse<JournalChatResponse>> => {
  try {
    // Simulate a short delay for realism
    await new Promise(resolve => setTimeout(resolve, 800));
    
    let response = '';
    
    // Generate a response based on available context
    if (context.entries && context.entries.length > 0) {
      const entry = context.entries[0]; // Most relevant entry
      
      if (entry) {
        response = `Based on your journal entries, I can see that on ${entry.date}, you wrote about "${entry.title}". ` +
          `This seems relevant to your question. ` +
          `Would you like to explore how this relates to your current thoughts or feelings?`;
        
        // Add a reference to a second entry if available
        if (context.entries.length > 1) {
          const secondEntry = context.entries[1];
          if (secondEntry) {
            response += `\n\nI also noticed that on ${secondEntry.date}, you mentioned "${secondEntry.title}". ` +
              `These entries might give us some insight into your patterns or experiences.`;
          }
        }
      } else {
        response = "I found some relevant journal entries, but I'm having trouble processing them right now.";
      }
      
      response += `\n\n(Note: This is a simulated response. For more personalized insights, please provide a Gemini API key in the app settings.)`;
    } else {
      response = "I don't have any journal entries that relate to your question. " +
        "Would you like to write about this topic in your journal? " +
        "Journaling about it could help you explore your thoughts more deeply." +
        "\n\n(Note: This is a simulated response. For more personalized insights, please provide a Gemini API key in the app settings.)";
    }

    return {
      success: true,
      data: {
        response,
        context
      }
    };
  } catch (error) {
    console.error('Error generating simulated response:', error);
    return {
      success: false,
      error: { 
        message: 'Failed to generate response',
        code: 'GENERATION_ERROR' 
      }
    };
  }
};

/**
 * Journal Chat AI Service
 * Service for generating AI responses for the Journal Chat feature
 * Uses local LLM (Ollama) with fallback to Gemini AI
 */

import { ApiResponse, AIServiceConfig, LLMMessage } from '@/types';
import { JournalChatResponse, JournalChatContext } from '@/types/journal-chat';
import { JournalEntry } from '@/types';
import { utcTimestampToLocalDateString } from '@/utils/dateUtils';
import { getAIConfig } from '@/config/ai.config';
import { callLocalLLM, LocalLLMError } from './localLLMService';
import { handleApiError } from '@/utils/errorHandler';

// Configuration - Use Vite environment variables (fallback for Gemini)
const DEFAULT_API_KEY = import.meta.env.VITE_GEMINI_API_KEY || null;

/**
 * Get configuration
 */
const getConfig = () => ({
  geminiApiKey: localStorage.getItem('gemini_api_key') || DEFAULT_API_KEY
});

/**
 * Check if local LLM service is available
 */
const isLocalLLMAvailable = async (): Promise<boolean> => {
  try {
    const aiConfig = getAIConfig();
    const endpoint = aiConfig.localLLMEndpoint || 'http://localhost:11434/v1/chat/completions';
    // Use the correct Ollama API endpoint for checking service availability
    const checkEndpoint = endpoint.replace('/v1/chat/completions', '/api/tags');
    const response = await fetch(checkEndpoint, {
      method: 'GET',
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });
    return response.ok;
  } catch (error) {
    console.warn('Local LLM service not available for journal chat:', error);
    return false;
  }
};

// Maximum number of entries to include in context
const MAX_CONTEXT_ENTRIES = 10; // Used in generateAIResponse

/**
 * Generate AI response using local LLM
 */
const generateLocalLLMResponse = async (
  query: string,
  context: JournalChatContext
): Promise<ApiResponse<JournalChatResponse>> => {
  try {
    const aiConfig = getAIConfig();

    // Build the prompt with the user's query and journal context
    const limitedEntries = context.entries.slice(0, MAX_CONTEXT_ENTRIES);
    const entriesText = limitedEntries.length > 0
      ? limitedEntries.map(entry => `Date: ${entry.date}\nTitle: ${entry.title}\nContent: ${entry.content}`).join('\n\n')
      : 'No relevant journal entries found.';

    const systemPrompt = `You are Amber, a friendly and empathetic journal assistant. You help users explore their thoughts and feelings through their journal entries.

Your role:
- Be conversational, warm, and supportive
- Provide thoughtful insights based on their journal content
- Ask follow-up questions to encourage deeper reflection
- Reference specific journal entries when relevant
- Be encouraging and non-judgmental

Guidelines:
- Keep responses conversational and engaging
- If there's not enough information, acknowledge this politely
- Focus on helping the user understand patterns or insights
- Encourage continued journaling and self-reflection`;

    const userPrompt = `The user asked: "${query}"

Based on their journal entries below, provide a helpful, thoughtful response:

Journal Entries:
${entriesText}

Please respond as Amber, their supportive journal assistant.`;

    const messages: LLMMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    const response = await callLocalLLM(messages, aiConfig);

    if (!response || typeof response !== 'string') {
      throw new LocalLLMError('invalid_response', 'Empty or invalid response from local LLM');
    }

    return {
      success: true,
      data: {
        response: response.trim(),
        context: context
      }
    };
  } catch (error) {
    console.error('Error generating local LLM response:', error);

    if (error instanceof LocalLLMError) {
      throw error; // Re-throw to allow fallback handling
    }

    throw new LocalLLMError('unknown_error', `Failed to generate response: ${error.message}`);
  }
};

/**
 * Format journal entries for prompt context
 */
export const formatEntriesForPrompt = (entries: JournalEntry[]): string => {
  return entries.map((entry) => {
    const date = utcTimestampToLocalDateString(entry.created_at);
    const summary = entry.ai_summary || '';
    const emotion = entry.emotion || '';
    const moodScore = entry.mood_score ? `${entry.mood_score}/10` : '';
    
    return `Date: ${date}
Title: ${entry.title}
${emotion ? `Emotion: ${emotion}` : ''}${moodScore ? ` (Mood: ${moodScore})` : ''}
${summary ? `Summary: ${summary}\n` : ''}Content: ${entry.content.slice(0, 300)}${entry.content.length > 300 ? '...' : ''}
---`;
  }).join('\n\n');
};

/**
 * Generate AI response using local LLM with Gemini fallback
 */
export const generateAIResponse = async (
  query: string,
  context: JournalChatContext
): Promise<ApiResponse<JournalChatResponse>> => {
  try {
    // First, try local LLM service
    const localLLMAvailable = await isLocalLLMAvailable();

    if (localLLMAvailable) {
      console.log('🤖 Using local LLM for journal chat response');
      try {
        return await generateLocalLLMResponse(query, context);
      } catch (error) {
        console.warn('Local LLM failed, falling back to Gemini:', error);
        // Continue to Gemini fallback
      }
    }

    // Fallback to Gemini API
    console.log('🌐 Using Gemini API for journal chat response');
    const config = getConfig();
    const apiKey = config.geminiApiKey;

    if (!apiKey) {
      console.warn('Gemini API key not found, falling back to simulated response');
      return generateSimulatedResponse(query, context);
    }
    
    // Build the prompt with the user's query and journal context - limit to max entries
    const limitedEntries = context.entries.slice(0, MAX_CONTEXT_ENTRIES);
    const entriesText = limitedEntries.length > 0 
      ? limitedEntries.map(entry => `Date: ${entry.date}\nTitle: ${entry.title}\nContent: ${entry.content}`).join('\n\n')
      : 'No relevant journal entries found.';
    
    const prompt = `You are Amber, a friendly and empathetic journal assistant.
The user asked: "${query}".

Based on their journal entries below, respond with helpful, thoughtful, and reflective insight.
Be conversational and warm. If relevant, include specific references to the journal content.
If there's not enough information in their journal to answer well, acknowledge this politely.

Journal Entries:
${entriesText}

Your response:`;

    // Call Gemini API
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': apiKey,
      },
      body: JSON.stringify({
        contents: [{
          role: 'user',
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 800,
        },
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini API error:', errorText);
      return {
        success: false,
        error: { 
          message: `AI service error: ${response.status} ${response.statusText}`,
          code: 'AI_SERVICE_ERROR'
        }
      };
    }

    const responseData = await response.json();
    
    // Extract the text from the response
    const aiResponse = responseData?.candidates?.[0]?.content?.parts?.[0]?.text || '';
    
    if (!aiResponse) {
      console.error('Empty response from Gemini API:', responseData);
      return {
        success: false,
        error: { 
          message: 'Empty response from AI service',
          code: 'EMPTY_RESPONSE'
        }
      };
    }

    // Return the AI-generated response
    return {
      success: true,
      data: {
        response: aiResponse,
        context: context
      }
    };
  } catch (error) {
    console.error('Error generating AI response:', error);

    // Fall back to simulated response on error
    console.log('Falling back to simulated response due to error');
    return generateSimulatedResponse(query, context);
  }
};

/**
 * Generate a simulated response when the AI service is unavailable
 * This is a fallback mechanism for when the API is down or rate limited
 */
export const generateSimulatedResponse = async (
  _query: string, // Prefixed with underscore to indicate it's not used
  context: JournalChatContext
): Promise<ApiResponse<JournalChatResponse>> => {
  try {
    // Simulate a short delay for realism
    await new Promise(resolve => setTimeout(resolve, 800));
    
    let response = '';
    
    // Generate a response based on available context
    if (context.entries && context.entries.length > 0) {
      const entry = context.entries[0]; // Most relevant entry
      
      if (entry) {
        response = `Based on your journal entries, I can see that on ${entry.date}, you wrote about "${entry.title}". ` +
          `This seems relevant to your question. ` +
          `Would you like to explore how this relates to your current thoughts or feelings?`;
        
        // Add a reference to a second entry if available
        if (context.entries.length > 1) {
          const secondEntry = context.entries[1];
          if (secondEntry) {
            response += `\n\nI also noticed that on ${secondEntry.date}, you mentioned "${secondEntry.title}". ` +
              `These entries might give us some insight into your patterns or experiences.`;
          }
        }
      } else {
        response = "I found some relevant journal entries, but I'm having trouble processing them right now.";
      }
      
      response += `\n\n(Note: This is a simulated response. For more personalized insights, please provide a Gemini API key in the app settings.)`;
    } else {
      response = "I don't have any journal entries that relate to your question. " +
        "Would you like to write about this topic in your journal? " +
        "Journaling about it could help you explore your thoughts more deeply." +
        "\n\n(Note: This is a simulated response. For more personalized insights, please provide a Gemini API key in the app settings.)";
    }

    return {
      success: true,
      data: {
        response,
        context
      }
    };
  } catch (error) {
    console.error('Error generating simulated response:', error);
    return {
      success: false,
      error: { 
        message: 'Failed to generate response',
        code: 'GENERATION_ERROR' 
      }
    };
  }
};

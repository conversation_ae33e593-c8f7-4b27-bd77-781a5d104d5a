/**
 * Journal Chat Types
 * Types for the Journal Chat feature (conversation and message persistence)
 */

/**
 * Role in a chat conversation
 */
export type ChatRole = 'user' | 'assistant';

/**
 * Journal chat context with relevant entries
 */
export interface JournalChatContext {
  entries: Array<{
    id: string;
    date: string;
    title: string;
    content: string;
    summary?: string;
    relevance: number;
  }>;
}

/**
 * Journal chat message
 */
export interface ChatMessage {
  id: string;
  conversationId?: string;
  role: ChatRole;
  content: string;
  timestamp: string;
  context?: JournalChatContext;
  isError?: boolean;
}

/**
 * Journal chat conversation
 */
export interface ChatConversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  isPinned: boolean;
  contextSize: number;
  messages?: ChatMessage[];
}

/**
 * Create chat conversation payload
 */
export interface CreateChatConversationPayload {
  title: string;
  isPinned?: boolean;
  contextSize?: number;
}

/**
 * Update chat conversation payload
 */
export interface UpdateChatConversationPayload {
  title?: string;
  isPinned?: boolean;
  contextSize?: number;
}

/**
 * Create chat message payload
 */
export interface CreateChatMessagePayload {
  conversationId: string;
  role: ChatRole;
  content: string;
  context?: JournalChatContext | undefined;
  isError?: boolean;
}

/**
 * API response type with data or error specifically for Journal Chat
 * This extends the global ApiResponse to handle potentially undefined data
 */
export interface ChatApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
  };
}

/**
 * Journal chat response from service
 */
export interface JournalChatResponse {
  response: string;
  context: JournalChatContext;
}

/**
 * Database to application model converters
 */

/**
 * Convert database conversation to application model
 */
export const dbToChatConversation = (dbConversation: any): ChatConversation => ({
  id: dbConversation.id,
  title: dbConversation.title,
  createdAt: dbConversation.created_at,
  updatedAt: dbConversation.updated_at,
  isPinned: dbConversation.is_pinned,
  contextSize: dbConversation.context_size,
  messages: dbConversation.messages ? dbConversation.messages.map(dbToChatMessage) : undefined,
});

/**
 * Convert database message to application model
 */
export const dbToChatMessage = (dbMessage: any): ChatMessage => ({
  id: dbMessage.id,
  conversationId: dbMessage.conversation_id,
  role: dbMessage.role as ChatRole,
  content: dbMessage.content,
  timestamp: dbMessage.created_at,
  context: dbMessage.context,
  isError: dbMessage.is_error,
});

/**
 * Convert application model to database insert payload
 */
export const chatConversationToDbInsert = (conversation: CreateChatConversationPayload): any => ({
  title: conversation.title,
  is_pinned: conversation.isPinned ?? false,
  context_size: conversation.contextSize ?? 10,
});

/**
 * Convert application model to database update payload
 */
export const chatConversationToDbUpdate = (conversation: UpdateChatConversationPayload): any => {
  const updatePayload: any = {};
  
  if (conversation.title !== undefined) {
    updatePayload.title = conversation.title;
  }
  
  if (conversation.isPinned !== undefined) {
    updatePayload.is_pinned = conversation.isPinned;
  }
  
  if (conversation.contextSize !== undefined) {
    updatePayload.context_size = conversation.contextSize;
  }
  
  return updatePayload;
};

/**
 * Convert application model to database insert payload for messages
 */
export const chatMessageToDbInsert = (message: CreateChatMessagePayload): any => ({
  conversation_id: message.conversationId,
  role: message.role,
  content: message.content,
  context: message.context,
  is_error: message.isError ?? false,
});

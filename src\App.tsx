import { useEffect, useState, Suspense, lazy } from 'react';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';
import { initializeApplication, InitializationResult } from '@/services/initialization.service';
import { createOptimizedQueryClient } from '@/config/queryClient.config';
import { getEnvironmentConfig } from '@/config/environment.config';
import { usePerformanceProvider } from '@/hooks/usePerformanceMonitoring';

// Lazy load route components for code splitting
const Index = lazy(() => import('./pages/Index'));
const Journal = lazy(() => import('./pages/Journal'));
const Write = lazy(() => import('./pages/Write'));
const Auth = lazy(() => import('./pages/Auth'));
const Settings = lazy(() => import('./pages/Settings'));
const MoodAnalytics = lazy(() => import('./pages/MoodAnalytics'));
const JournalChat = lazy(() => import('./pages/JournalChat'));
const MemoryTest = lazy(() => import('./pages/MemoryTest').then(module => ({ default: module.MemoryTest })));
const NotFound = lazy(() => import('./pages/NotFound'));

// Lazy load development components
const PerformanceDashboard = lazy(() => import('@/components/dev/PerformanceDashboard').then(module => ({ default: module.PerformanceDashboard })));

// Create optimized QueryClient instance
const queryClient = createOptimizedQueryClient();

/**
 * Initialization loading component
 */
const InitializationLoader = ({ result }: { result: InitializationResult | null }) => {
  if (!result) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 to-orange-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-amber-800 mb-2">Initializing Amberglow</h2>
          <p className="text-amber-600">Setting up your journal experience...</p>
        </div>
      </div>
    );
  }

  if (!result.canProceed) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-red-800 mb-4">Configuration Error</h2>
          <p className="text-red-600 mb-4">
            Amberglow cannot start due to configuration issues. Please check the console for
            details.
          </p>
          <div className="bg-red-100 border border-red-300 rounded-lg p-4 text-left">
            <h3 className="font-semibold text-red-800 mb-2">Common fixes:</h3>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• Check your .env file exists and contains all required variables</li>
              <li>• Verify your Supabase URL and API key are correct</li>
              <li>• Ensure all environment variables are properly formatted</li>
              <li>• Restart your development server after making changes</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

const App = () => {
  const [initResult, setInitResult] = useState<InitializationResult | null>(null);

  useEffect(() => {
    // Initialize application on mount
    initializeApplication()
      .then(setInitResult)
      .catch(error => {
        console.error('Initialization failed:', error);
        // Create a fallback result to allow the app to continue
        setInitResult({
          success: false,
          canProceed: true, // Allow app to proceed even if init fails
          environment: 'development',
          errors: [error.message || 'Unknown initialization error'],
          warnings: [],
          timestamp: new Date().toISOString(),
        });
      });
  }, []);

  // Show initialization loader while app is starting
  if (!initResult) {
    return <InitializationLoader result={initResult} />;
  }

  // Basic app structure with error boundaries and semantic HTML
  return (
    <ErrorBoundary
      componentName="App"
      showErrorDetails={getEnvironmentConfig().isDevelopment}
      errorMessage="The application encountered an unexpected error. Please refresh the page to continue."
    >
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <ErrorBoundary
              componentName="Router"
              errorMessage="Navigation error occurred. Please try refreshing the page."
            >
              <AuthProvider>
                <ErrorBoundary
                  componentName="Routes"
                  errorMessage="Page loading error. Please try navigating to a different page."
                >
                  <Suspense
                    fallback={
                      <main
                        id="main-content"
                        className="min-h-screen flex items-center justify-center"
                        role="main"
                        aria-label="Loading application"
                      >
                        <LoadingSpinner size="lg" message="Loading page..." />
                      </main>
                    }
                  >
                    <Routes>
                      <Route path="/" element={<Index />} />
                      <Route path="/journal" element={<Journal />} />
                      <Route path="/journal-chat" element={<JournalChat />} />
                      <Route path="/write" element={<Write />} />
                      <Route path="/auth" element={<Auth />} />
                      <Route path="/settings" element={<Settings />} />
                      <Route path="/analytics" element={<MoodAnalytics />} />
                      <Route path="/memory-test" element={<MemoryTest />} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Suspense>
                </ErrorBoundary>

                {/* Performance monitoring - re-enabled after fixing infinite render issue */}
                {initResult?.success && (
                  <ErrorBoundary
                    componentName="PerformanceMonitoring"
                    errorMessage="Performance monitoring error. The app will continue to work normally."
                  >
                    <PerformanceMonitoringWrapper />
                  </ErrorBoundary>
                )}
              </AuthProvider>
            </ErrorBoundary>
          </BrowserRouter>

          {/* React Query DevTools - only in development */}
          {getEnvironmentConfig().isDevelopment && <ReactQueryDevtools initialIsOpen={false} />}
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

/**
 * Performance monitoring wrapper component
 * Only initializes after the main app is successfully loaded
 */
const PerformanceMonitoringWrapper = () => {
  // Initialize performance monitoring safely
  usePerformanceProvider();

  return (
    <>
      {/* Performance Dashboard - only in development */}
      {getEnvironmentConfig().isDevelopment && (
        <Suspense fallback={null}>
          <PerformanceDashboard />
        </Suspense>
      )}
    </>
  );
};

export default App;

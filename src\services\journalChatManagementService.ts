/**
 * Journal Chat Management Service
 * Service for managing journal chat conversations and messages
 */

import { supabase } from '@/integrations/supabase/client';
import { 
  ChatConversation,
  ChatMessage,
  CreateChatConversationPayload,
  CreateChatMessagePayload,
  UpdateChatConversationPayload,
  dbToChatConversation,
  chatConversationToDbInsert,
  dbToChatMessage,
  chatMessageToDbInsert,
  chatConversationToDbUpdate
} from '@/types/journal-chat';

// Use this custom API response type that properly handles undefined data
type SafeApiResponse<T> = {
  success: true;
  data?: T | undefined;
} | {
  success: false;
  error?: {
    message: string;
    code: string;
  } | undefined;
};

// Debug flag for additional logging
const DEBUG = true;

/**
 * Log debug information if debug mode is enabled
 */
const debugLog = (message: string, data?: any) => {
  if (DEBUG) {
    console.log(`[JournalChatService] ${message}`, data || '');
  }
};

/**
 * Create a new chat conversation
 */
export const createChatConversation = async (
  payload: CreateChatConversationPayload
): Promise<SafeApiResponse<ChatConversation>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    const insertData = {
      user_id: user.id,
      ...chatConversationToDbInsert(payload)
    };

    // Use type assertion to handle new tables not in Supabase type definitions
    debugLog('Inserting conversation data:', insertData);
    const { data, error } = await supabase
      .from('journal_chat_conversations' as any)
      .insert(insertData)
      .select()
      .single();
    
    if (error) {
      debugLog('Error inserting conversation:', error);
    } else {
      debugLog('Successfully created conversation:', data);
    }

    if (error) {
      console.error('Error creating chat conversation:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    return {
      success: true,
      data: dbToChatConversation(data)
    };
  } catch (error) {
    console.error('Unexpected error creating chat conversation:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Get all chat conversations for current user
 */
export const getChatConversations = async (
  limit: number = 50,
  orderBy: 'created_at' | 'updated_at' | 'title' = 'updated_at',
  order: 'asc' | 'desc' = 'desc'
): Promise<SafeApiResponse<ChatConversation[]>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    // Build query with ordering
    // Use type assertion to handle new tables not in Supabase type definitions
    let query = supabase
      .from('journal_chat_conversations' as any)
      .select('*')
      .eq('user_id', user.id);
    
    // Order by specified field and direction
    query = query.order(orderBy, { ascending: order === 'asc' });
    
    // Apply limit
    query = query.limit(limit);
    
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching chat conversations:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    // Convert to app model and return
    return {
      success: true,
      data: data ? data.map(dbToChatConversation) : []
    } as SafeApiResponse<ChatConversation[]>;
  } catch (error) {
    console.error('Unexpected error fetching chat conversations:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Get a single chat conversation by ID
 */
export const getChatConversationById = async (
  conversationId: string
): Promise<SafeApiResponse<ChatConversation>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    // Use type assertion to handle new tables not in Supabase type definitions
    const { data, error } = await supabase
      .from('journal_chat_conversations' as any)
      .select('*')
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error fetching chat conversation:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    return {
      success: true,
      data: data ? dbToChatConversation(data) : undefined
    };
  } catch (error) {
    console.error('Unexpected error fetching chat conversation:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Update a chat conversation
 */
export const updateChatConversation = async (
  conversationId: string,
  payload: UpdateChatConversationPayload
): Promise<SafeApiResponse<ChatConversation>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    const updateData = chatConversationToDbUpdate(payload);
    
    // Don't proceed if there's nothing to update
    if (Object.keys(updateData).length === 0) {
      return {
        success: false,
        error: { message: 'No fields to update', code: 'VALIDATION_ERROR' }
      };
    }

    // Use type assertion to handle new tables not in Supabase type definitions
    const { data, error } = await supabase
      .from('journal_chat_conversations' as any)
      .update(updateData)
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating chat conversation:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    return {
      success: true,
      data: data ? dbToChatConversation(data) : undefined
    };
  } catch (error) {
    console.error('Unexpected error updating chat conversation:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Delete a chat conversation
 */
export const deleteChatConversation = async (
  conversationId: string
): Promise<SafeApiResponse<void>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    // Use type assertion to handle new tables not in Supabase type definitions
    const { error } = await supabase
      .from('journal_chat_conversations' as any)
      .delete()
      .eq('id', conversationId)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error deleting chat conversation:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    return {
      success: true
    };
  } catch (error) {
    console.error('Unexpected error deleting chat conversation:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Get messages for a conversation
 */
export const getChatMessages = async (
  conversationId: string,
  limit: number = 100
): Promise<SafeApiResponse<ChatMessage[]>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    // First verify the user has access to this conversation
    // Use type assertion to handle new tables not in Supabase type definitions
    const { data: conversationData, error: conversationError } = await supabase
      .from('journal_chat_conversations' as any)
      .select('id')
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (conversationError || !conversationData) {
      console.error('Error verifying conversation access:', conversationError);
      return {
        success: false,
        error: { message: 'Conversation not found or access denied', code: 'PERMISSION_DENIED' }
      };
    }

    // Get messages
    // Use type assertion to handle new tables not in Supabase type definitions
    const { data, error } = await supabase
      .from('journal_chat_messages' as any)
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching chat messages:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    // Convert messages from DB format
    const messages = data ? data.map(dbToChatMessage) : [];

    return {
      success: true,
      data: messages
    };
  } catch (error) {
    console.error('Unexpected error fetching chat messages:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Add a message to a conversation
 */
export const addChatMessage = async (
  payload: CreateChatMessagePayload
): Promise<SafeApiResponse<ChatMessage>> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated', code: 'AUTH_ERROR' }
      };
    }

    // First verify the user has access to this conversation
    // Use type assertion to handle new tables not in Supabase type definitions
    const { data: conversationData, error: conversationError } = await supabase
      .from('journal_chat_conversations' as any)
      .select('id')
      .eq('id', payload.conversationId)
      .eq('user_id', user.id)
      .single();

    if (conversationError || !conversationData) {
      console.error('Error verifying conversation access:', conversationError);
      return {
        success: false,
        error: { message: 'Conversation not found or access denied', code: 'PERMISSION_DENIED' }
      };
    }

    // Insert the message
    const insertData = chatMessageToDbInsert(payload);
    
    // Use type assertion to handle new tables not in Supabase type definitions
    const { data, error } = await supabase
      .from('journal_chat_messages' as any)
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Error adding chat message:', error);
      return {
        success: false,
        error: { message: error.message, code: 'DATABASE_ERROR' }
      };
    }

    // Update the conversation's updated_at timestamp
    // Use type assertion to handle new tables not in Supabase type definitions
    await supabase
      .from('journal_chat_conversations' as any)
      .update({ updated_at: new Date().toISOString() })
      .eq('id', payload.conversationId);

    return {
      success: true,
      data: dbToChatMessage(data)
    };
  } catch (error) {
    console.error('Unexpected error adding chat message:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Get a conversation with its messages
 */
export const getChatConversationWithMessages = async (
  conversationId: string,
  messageLimit: number = 100
): Promise<SafeApiResponse<ChatConversation>> => {
  try {
    // Get the conversation
    const conversationResult = await getChatConversationById(conversationId);
    if (!conversationResult.success) {
      return conversationResult;
    }

    // Get messages
    const messagesResult = await getChatMessages(conversationId, messageLimit);
    if (!messagesResult.success) {
      return {
        success: false,
        error: messagesResult.error
      } as SafeApiResponse<ChatConversation>;
    }

    // Combine
    const conversation = conversationResult.data;
    if (conversation) {
      // Make sure messages are defined or set to empty array
      conversation.messages = messagesResult.success && messagesResult.data ? messagesResult.data : [];

      return {
        success: true,
        data: conversation
      } as SafeApiResponse<ChatConversation>;
    }
    
    return {
      success: false,
      error: { message: 'Failed to retrieve conversation', code: 'NOT_FOUND' }
    } as SafeApiResponse<ChatConversation>;
  } catch (error) {
    console.error('Unexpected error getting conversation with messages:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR'
      }
    } as SafeApiResponse<ChatConversation>;
  }
}

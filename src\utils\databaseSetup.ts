/**
 * Database Setup Utility
 * Checks and creates necessary database tables for journal chat functionality
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Check if journal chat tables exist
 */
export const checkJournalChatTables = async (): Promise<{
  conversationsTableExists: boolean;
  messagesTableExists: boolean;
  error?: string;
}> => {
  try {
    // Try to query the conversations table
    const { error: conversationsError } = await supabase
      .from('journal_chat_conversations' as any)
      .select('id')
      .limit(1);

    // Try to query the messages table
    const { error: messagesError } = await supabase
      .from('journal_chat_messages' as any)
      .select('id')
      .limit(1);

    return {
      conversationsTableExists: !conversationsError,
      messagesTableExists: !messagesError,
      error: conversationsError?.message || messagesError?.message
    };
  } catch (error) {
    return {
      conversationsTableExists: false,
      messagesTableExists: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Create journal chat tables if they don't exist
 * Note: This requires database admin privileges
 */
export const createJournalChatTables = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Create conversations table
    const conversationsSQL = `
      CREATE TABLE IF NOT EXISTS journal_chat_conversations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        is_pinned BOOLEAN NOT NULL DEFAULT FALSE,
        context_size INT NOT NULL DEFAULT 10
      );
    `;

    // Create messages table
    const messagesSQL = `
      CREATE TABLE IF NOT EXISTS journal_chat_messages (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        conversation_id UUID NOT NULL REFERENCES journal_chat_conversations(id) ON DELETE CASCADE,
        role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
        content TEXT NOT NULL,
        context JSONB,
        embedding TEXT,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        is_error BOOLEAN NOT NULL DEFAULT FALSE
      );
    `;

    // Enable RLS
    const rlsSQL = `
      ALTER TABLE journal_chat_conversations ENABLE ROW LEVEL SECURITY;
      ALTER TABLE journal_chat_messages ENABLE ROW LEVEL SECURITY;
      
      -- Policies for conversations
      CREATE POLICY IF NOT EXISTS journal_chat_conversations_select ON journal_chat_conversations 
        FOR SELECT USING (auth.uid() = user_id);
      
      CREATE POLICY IF NOT EXISTS journal_chat_conversations_insert ON journal_chat_conversations 
        FOR INSERT WITH CHECK (auth.uid() = user_id);
      
      CREATE POLICY IF NOT EXISTS journal_chat_conversations_update ON journal_chat_conversations 
        FOR UPDATE USING (auth.uid() = user_id);
      
      CREATE POLICY IF NOT EXISTS journal_chat_conversations_delete ON journal_chat_conversations 
        FOR DELETE USING (auth.uid() = user_id);
      
      -- Policies for messages
      CREATE POLICY IF NOT EXISTS journal_chat_messages_select ON journal_chat_messages 
        FOR SELECT USING (
          EXISTS (
            SELECT 1 FROM journal_chat_conversations 
            WHERE id = conversation_id AND user_id = auth.uid()
          )
        );
      
      CREATE POLICY IF NOT EXISTS journal_chat_messages_insert ON journal_chat_messages 
        FOR INSERT WITH CHECK (
          EXISTS (
            SELECT 1 FROM journal_chat_conversations 
            WHERE id = conversation_id AND user_id = auth.uid()
          )
        );
    `;

    // Execute SQL commands (Note: This might not work with standard Supabase client)
    console.warn('Database table creation requires admin privileges. Please run the migration manually.');
    
    return {
      success: false,
      error: 'Table creation requires database admin privileges. Please run the migration manually or ensure Docker/Supabase is running.'
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

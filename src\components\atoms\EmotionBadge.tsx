/**
 * Emotion Badge Atom
 * Reusable badge component for displaying emotions with robust error handling
 */

import { Badge } from '@/components/ui/badge';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';
import {
  <PERSON>,
  Smile,
  <PERSON>,
  <PERSON>rk<PERSON>,
  <PERSON>own,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  CloudRain,
  HelpCircle
} from 'lucide-react';

/**
 * Valid emotion types that can be displayed in the badge
 * Add new emotions here and update the emotionColors mapping below
 */
export type ValidEmotion =
  | 'joyful'
  | 'calm'
  | 'neutral'
  | 'sad'
  | 'anxious'
  | 'excited'
  | 'grateful'
  | 'frustrated'
  | 'hopeful'
  | 'overwhelmed';

interface EmotionBadgeProps extends BaseComponentProps {
  /** Emotion to display - can be undefined or any string for defensive programming */
  emotion?: string | null;
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show icon */
  showIcon?: boolean;
}

/**
 * Emotion color and icon mapping
 * When adding new emotions:
 * 1. Add the emotion to the ValidEmotion type above
 * 2. Add the corresponding entry here with icon and Tailwind classes
 * 3. Ensure the emotion name matches exactly (case-sensitive)
 */
const emotionConfig: Record<ValidEmotion, {
  icon: React.ComponentType<{ className?: string }>;
  colorClass: string;
  gradient: string;
}> = {
  joyful: {
    icon: Smile,
    colorClass: 'text-amber-700 border-amber-200',
    gradient: 'bg-gradient-to-r from-yellow-50 to-amber-100 hover:from-yellow-100 hover:to-amber-200'
  },
  calm: {
    icon: Sun,
    colorClass: 'text-blue-700 border-blue-200',
    gradient: 'bg-gradient-to-r from-sky-50 to-blue-100 hover:from-sky-100 hover:to-blue-200'
  },
  neutral: {
    icon: Meh,
    colorClass: 'text-gray-700 border-gray-200',
    gradient: 'bg-gradient-to-r from-gray-50 to-slate-100 hover:from-gray-100 hover:to-slate-200'
  },
  sad: {
    icon: Frown,
    colorClass: 'text-indigo-700 border-indigo-200',
    gradient: 'bg-gradient-to-r from-indigo-50 to-blue-100 hover:from-indigo-100 hover:to-blue-200'
  },
  anxious: {
    icon: CloudRain,
    colorClass: 'text-purple-700 border-purple-200',
    gradient: 'bg-gradient-to-r from-purple-50 to-violet-100 hover:from-purple-100 hover:to-violet-200'
  },
  excited: {
    icon: Sparkles,
    colorClass: 'text-orange-700 border-orange-200',
    gradient: 'bg-gradient-to-r from-orange-50 to-amber-100 hover:from-orange-100 hover:to-amber-200'
  },
  grateful: {
    icon: Heart,
    colorClass: 'text-emerald-700 border-emerald-200',
    gradient: 'bg-gradient-to-r from-emerald-50 to-green-100 hover:from-emerald-100 hover:to-green-200'
  },
  frustrated: {
    icon: Angry,
    colorClass: 'text-red-700 border-red-200',
    gradient: 'bg-gradient-to-r from-red-50 to-rose-100 hover:from-red-100 hover:to-rose-200'
  },
  hopeful: {
    icon: Star,
    colorClass: 'text-pink-700 border-pink-200',
    gradient: 'bg-gradient-to-r from-pink-50 to-rose-100 hover:from-pink-100 hover:to-rose-200'
  },
  overwhelmed: {
    icon: Brain,
    colorClass: 'text-slate-700 border-slate-200',
    gradient: 'bg-gradient-to-r from-slate-50 to-gray-100 hover:from-slate-100 hover:to-gray-200'
  },
};

const sizeClasses = {
  sm: 'text-xs px-2 py-1 gap-1',
  md: 'text-sm px-3 py-1.5 gap-1.5',
  lg: 'text-base px-4 py-2 gap-2',
};

const iconSizeClasses = {
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-5 h-5',
};

/**
 * Type guard to check if a string is a valid emotion
 * @param emotion - The emotion string to validate
 * @returns True if the emotion is valid, false otherwise
 */
const isValidEmotion = (emotion: string): emotion is ValidEmotion => {
  return emotion in emotionConfig;
};

/**
 * Fallback configuration for invalid or undefined emotions
 */
const fallbackConfig = {
  icon: HelpCircle,
  colorClass: 'text-gray-700 border-gray-200',
  gradient: 'bg-gradient-to-r from-gray-50 to-slate-100 hover:from-gray-100 hover:to-slate-200',
  displayText: 'Unknown Emotion',
};

export const EmotionBadge = ({
  emotion,
  size = 'md',
  showIcon = false,
  className,
  testId,
}: EmotionBadgeProps) => {
  // Defensive programming: handle undefined, null, empty string, and invalid emotions
  let config = fallbackConfig;
  let displayText = fallbackConfig.displayText;

  if (emotion && typeof emotion === 'string' && emotion.trim() !== '') {
    const trimmedEmotion = emotion.trim().toLowerCase();

    // Check if the emotion exists in our mapping
    if (isValidEmotion(trimmedEmotion)) {
      config = emotionConfig[trimmedEmotion];
      displayText = trimmedEmotion;
    } else {
      // For invalid emotions, still show the original text but with fallback styling
      displayText = emotion;
    }
  }

  const IconComponent = config.icon;

  return (
    <Badge
      variant="secondary"
      className={cn(
        'capitalize font-medium transition-all duration-200 border',
        'flex items-center justify-center',
        config.gradient,
        config.colorClass,
        sizeClasses[size],
        className
      )}
      data-testid={testId}
    >
      {showIcon && (
        <IconComponent className={cn('flex-shrink-0', iconSizeClasses[size])} />
      )}
      <span>{displayText}</span>
    </Badge>
  );
};

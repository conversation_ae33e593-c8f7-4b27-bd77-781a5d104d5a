/**
 * EmotionPicker Component Tests
 * Tests to verify emotion picker behavior and form submission fix
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { EmotionPicker } from '@/components/features/EmotionPicker';
import { EmotionType } from '@/types';

describe('EmotionPicker', () => {
  const mockOnEmotionSelect = vi.fn();

  beforeEach(() => {
    mockOnEmotionSelect.mockClear();
  });

  it('renders all emotion options', () => {
    render(
      <EmotionPicker
        selectedEmotion=""
        onEmotionSelect={mockOnEmotionSelect}
      />
    );

    // Check that all emotion buttons are rendered
    expect(screen.getByText('grateful')).toBeInTheDocument();
    expect(screen.getByText('joyful')).toBeInTheDocument();
    expect(screen.getByText('calm')).toBeInTheDocument();
    expect(screen.getByText('excited')).toBeInTheDocument();
    expect(screen.getByText('anxious')).toBeInTheDocument();
    expect(screen.getByText('sad')).toBeInTheDocument();
    expect(screen.getByText('frustrated')).toBeInTheDocument();
    expect(screen.getByText('neutral')).toBeInTheDocument();
    expect(screen.getByText('hopeful')).toBeInTheDocument();
    expect(screen.getByText('overwhelmed')).toBeInTheDocument();
  });

  it('calls onEmotionSelect when emotion button is clicked', () => {
    render(
      <EmotionPicker
        selectedEmotion=""
        onEmotionSelect={mockOnEmotionSelect}
      />
    );

    const joyfulButton = screen.getByText('joyful').closest('button');
    fireEvent.click(joyfulButton!);

    expect(mockOnEmotionSelect).toHaveBeenCalledWith('joyful');
    expect(mockOnEmotionSelect).toHaveBeenCalledTimes(1);
  });

  it('highlights selected emotion', () => {
    render(
      <EmotionPicker
        selectedEmotion="joyful"
        onEmotionSelect={mockOnEmotionSelect}
      />
    );

    const joyfulButton = screen.getByText('joyful').closest('button');
    expect(joyfulButton).toHaveClass('bg-gradient-to-br');
    expect(joyfulButton).toHaveClass('from-amber-400');
    expect(joyfulButton).toHaveClass('to-orange-500');
  });

  it('has correct button type to prevent form submission', () => {
    render(
      <EmotionPicker
        selectedEmotion=""
        onEmotionSelect={mockOnEmotionSelect}
      />
    );

    const emotionButtons = screen.getAllByRole('button');
    emotionButtons.forEach(button => {
      expect(button).toHaveAttribute('type', 'button');
    });
  });

  it('does not trigger form submission when clicked inside a form', () => {
    const mockFormSubmit = vi.fn();
    
    render(
      <form onSubmit={mockFormSubmit}>
        <EmotionPicker
          selectedEmotion=""
          onEmotionSelect={mockOnEmotionSelect}
        />
        <button type="submit">Submit Form</button>
      </form>
    );

    const joyfulButton = screen.getByText('joyful').closest('button');
    fireEvent.click(joyfulButton!);

    // Emotion selection should be called
    expect(mockOnEmotionSelect).toHaveBeenCalledWith('joyful');
    
    // Form submission should NOT be triggered
    expect(mockFormSubmit).not.toHaveBeenCalled();
  });

  it('allows multiple emotion selections without form submission', () => {
    const mockFormSubmit = vi.fn();
    
    render(
      <form onSubmit={mockFormSubmit}>
        <EmotionPicker
          selectedEmotion=""
          onEmotionSelect={mockOnEmotionSelect}
        />
        <button type="submit">Submit Form</button>
      </form>
    );

    // Click multiple emotions
    const joyfulButton = screen.getByText('joyful').closest('button');
    const calmButton = screen.getByText('calm').closest('button');
    
    fireEvent.click(joyfulButton!);
    fireEvent.click(calmButton!);

    // Both emotion selections should be called
    expect(mockOnEmotionSelect).toHaveBeenCalledWith('joyful');
    expect(mockOnEmotionSelect).toHaveBeenCalledWith('calm');
    expect(mockOnEmotionSelect).toHaveBeenCalledTimes(2);
    
    // Form submission should still NOT be triggered
    expect(mockFormSubmit).not.toHaveBeenCalled();
  });
});

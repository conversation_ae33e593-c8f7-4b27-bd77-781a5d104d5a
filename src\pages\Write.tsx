/**
 * Write Page
 * Dedicated page component for creating new journal entries
 */

import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import PageLayout from '@/components/layout/PageLayout';
import { JournalEntry } from '@/components/features/JournalEntry';

import { useAuth } from '@/contexts/AuthContext';
import { cacheUtils } from '@/config/queryClient.config';
import { FormattedJournalEntry } from '@/types';

const Write = () => {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  console.log('📝 Write page render:', {
    user: user?.id,
    authLoading,
  });

  // Scroll to top when page loads
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/auth');
    }
  }, [user, authLoading, navigate]);

  // Handler for new journal entry
  const handleNewEntry = (entry: FormattedJournalEntry) => {
    console.log('📝 New entry created, invalidating cache and navigating:', { entryId: entry.id, title: entry.title });

    // Invalidate all journal entries queries to ensure fresh data on Journal page
    if (user?.id) {
      cacheUtils.invalidateJournalEntries(queryClient, user.id);
      console.log('✅ Cache invalidated for user:', user.id);
    }

    // Navigate to journal page with state to trigger refresh
    navigate('/journal', {
      state: {
        from: '/write',
        refresh: true,
        newEntryId: entry.id
      }
    });
  };

  // Handler for canceling entry creation
  const handleCancel = () => {
    navigate('/journal');
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!user) {
    return null;
  }

  return (
    <PageLayout>
      <JournalEntry onSave={handleNewEntry} onCancel={handleCancel} />
    </PageLayout>
  );
};

export default Write;

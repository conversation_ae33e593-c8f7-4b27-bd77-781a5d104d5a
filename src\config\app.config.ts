/**
 * Application Configuration
 * Centralized configuration for the Amberglow application
 */

interface AppConfig {
  // AI Configuration
  geminiApiKey: string | null;
  openAiApiKey: string | null;
  localLlmEndpoint: string | null;
  
  // UI Configuration
  theme: 'light' | 'dark' | 'system';
  animationsEnabled: boolean;
  
  // Feature Flags
  enableDebugMode: boolean;
  enableAIFeatures: boolean;
  enableMemoryFeatures: boolean;
  
  // Memory Configuration
  memoryImportanceThreshold: number;
  memorySimilarityThreshold: number;
  
  // Journal Chat Configuration
  maxJournalContextEntries: number;
  minJournalRelevanceThreshold: number;
}

/**
 * Default configuration
 */
const defaultConfig: AppConfig = {
  // AI Configuration
  geminiApiKey: null,
  openAiApiKey: null,
  localLlmEndpoint: null,
  
  // UI Configuration
  theme: 'system',
  animationsEnabled: true,
  
  // Feature Flags
  enableDebugMode: import.meta.env.DEV,
  enableAIFeatures: true,
  enableMemoryFeatures: true,
  
  // Memory Configuration
  memoryImportanceThreshold: 4,
  memorySimilarityThreshold: 0.92,
  
  // Journal Chat Configuration
  maxJournalContextEntries: 10,
  minJournalRelevanceThreshold: 0.2
};

// Runtime configuration that can be updated during app execution
let runtimeConfig = { ...defaultConfig };

/**
 * Update config at runtime
 */
export const updateConfig = (partialConfig: Partial<AppConfig>): AppConfig => {
  runtimeConfig = {
    ...runtimeConfig,
    ...partialConfig
  };
  return runtimeConfig;
};

/**
 * Get current configuration
 */
export const getConfig = (): AppConfig => {
  return { ...runtimeConfig };
};

/**
 * Reset configuration to defaults
 */
export const resetConfig = (): AppConfig => {
  runtimeConfig = { ...defaultConfig };
  return runtimeConfig;
};

export type { AppConfig };

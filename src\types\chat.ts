/**
 * Chat related types for the Journal Chat feature
 */

/**
 * Journal chat context with relevant entries
 */
export interface JournalChatContext {
  entries: Array<{
    id: string;
    date: string;
    title: string;
    content: string;
    summary?: string;
    relevance: number;
  }>;
}

/**
 * Role in a chat conversation
 */
export type ChatRole = 'user' | 'assistant';

/**
 * Chat message structure
 */
export interface ChatMessage {
  id: string;
  role: ChatRole;
  content: string;
  timestamp: string;
  context?: JournalChatContext;
  isError?: boolean;
}

/**
 * Journal chat response from service
 */
export interface JournalChatResponse {
  response: string;
  context: JournalChatContext;
}

/**
 * JournalChat Page
 * Allows users to have conversations with <PERSON> about their journal entries
 * Using RAG (Retrieval Augmented Generation) approach with semantic embeddings
 */

import React, { useState, useRef, useEffect } from 'react';
import PageLayout from '@/components/layout/PageLayout';
import { LoadingSpinner } from '@/components/atoms';
import { generateJournalChatResponse } from '../services/journalChatService';
import { useAuth } from '@/contexts/AuthContext';
import { ChatMessage, ChatConversation } from '@/types/journal-chat';
import { 
  createChatConversation, 
  getChatConversations, 
  getChatConversationWithMessages,
  addChatMessage,
  deleteChatConversation
} from '../services/journalChatManagementService';
import { Button } from '@/components/ui/button';
import { PlusIcon, Trash2Icon, MessageSquareIcon } from 'lucide-react';
import { checkJournalChatTables } from '@/utils/databaseSetup';

/**
 * JournalChat component
 */
const JournalChat: React.FC = () => {
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(true);
  const [isLoadingConversations, setIsLoadingConversations] = useState<boolean>(false);
  const [isCreatingConversation, setIsCreatingConversation] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const { user } = useAuth();

  // Add error state for better error handling
  const [error, setError] = useState<string | null>(null);
  
  // Check database tables and load conversations on mount
  useEffect(() => {
    if (user) {
      // First check if tables exist
      checkJournalChatTables().then(tableCheck => {
        if (!tableCheck.conversationsTableExists || !tableCheck.messagesTableExists) {
          setError(`Database tables missing. Please ensure the journal chat migration has been run. Missing: ${!tableCheck.conversationsTableExists ? 'conversations table' : ''} ${!tableCheck.messagesTableExists ? 'messages table' : ''}. Error: ${tableCheck.error || 'Unknown'}`);
          return;
        }

        // Tables exist, load conversations
        loadConversations().catch(err => {
          console.error('Failed to load conversations on mount:', err);
          setError(`Unable to load conversations: ${err.message || err}. Please check if the database tables exist.`);
        });
      }).catch(err => {
        console.error('Failed to check database tables:', err);
        setError(`Database connection error: ${err.message || err}. Please check your Supabase connection.`);
      });
    }
  }, [user]);

  // Load conversation messages when currentConversationId changes
  useEffect(() => {
    if (currentConversationId) {
      loadConversationMessages(currentConversationId);
    } else if (conversations && conversations.length > 0) {
      // If we have conversations but none selected, select the most recent one
      const firstConversation = conversations[0];
      if (firstConversation && firstConversation.id) {
        setCurrentConversationId(firstConversation.id);
      }
    } else {
      // If no conversations exist, show welcome message
      setMessages([
        {
          id: 'welcome',
          role: 'assistant',
          content: "Hi there! I'm Amber, your journal companion. I can help you reflect on your past entries and thoughts. Create a new chat to get started!",
          timestamp: new Date().toISOString(),
        },
      ]);
    }
  }, [currentConversationId, conversations]);

  // Scroll to bottom of chat whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on load and when conversation changes
  useEffect(() => {
    if (currentConversationId) {
      inputRef.current?.focus();
    }
  }, [currentConversationId]);

  // Load all conversations
  const loadConversations = async () => {
    // Reset error state when attempting to load
    setError(null);
    if (!user) return;

    setIsLoadingConversations(true);
    try {
      const result = await getChatConversations();
      if (result.success && result.data) {
        setConversations(result.data);
      } else {
        // Type guard: only access error in the failure case
        const errorMsg = !result.success ? result.error?.message || 'Unknown error' : 'Unknown error';
        console.error('Error loading conversations:', errorMsg);
        setError(`Failed to load conversations: ${errorMsg}`);
      }
    } catch (error) {
      console.error('Failed to load conversations:', error);
      setError(`Failed to load conversations: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoadingConversations(false);
    }
  };

  // Load messages for a conversation
  const loadConversationMessages = async (conversationId: string) => {
    // Reset error state when attempting to load
    setError(null);
    if (!user) return;
    
    setIsLoading(true);
    try {
      const result = await getChatConversationWithMessages(conversationId);
      if (result.success && result.data && result.data.messages) {
        setMessages(result.data.messages);
      } else {
        // Type guard: only access error in the failure case
        console.error('Error loading conversation messages:', !result.success ? result.error : 'Unknown error');
        setMessages([]);
      }
    } catch (error) {
      console.error('Failed to load conversation messages:', error);
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Create a new conversation
  const handleCreateNewChat = async () => {
    if (!user) return;
    
    setIsCreatingConversation(true);
    try {
      const defaultTitle = `Chat ${new Date().toLocaleDateString()}`;
      const result = await createChatConversation({ title: defaultTitle });
      
      if (result.success) {
        // Add the new conversation to the list and select it
        if (result.data) {
          setConversations(prevConversations => [
            result.data as ChatConversation, 
            ...prevConversations
          ]);
          setCurrentConversationId(result.data.id);
        }
        setMessages([]);
      } else {
        // Type guard: only access error in the failure case
        console.error('Error creating new conversation:', !result.success ? result.error : 'Unknown error');
      }
    } catch (error) {
      console.error('Failed to create new conversation:', error);
    } finally {
      setIsCreatingConversation(false);
    }
  };
  
  // Delete a conversation
  const handleDeleteConversation = async (conversationId: string) => {
    if (!user || !conversationId) return;
    
    try {
      const result = await deleteChatConversation(conversationId);
      if (result.success) {
        // Remove the conversation from the list
        setConversations(prevConversations => 
          prevConversations.filter(conv => conv.id !== conversationId)
        );
        
        // If we deleted the current conversation, select another one
        if (currentConversationId === conversationId) {
          // Make sure conversations exists and filter out the deleted one
          const remainingConversations = conversations ? conversations.filter(
            conv => conv && conv.id !== conversationId
          ) : [];
          
          if (remainingConversations && remainingConversations.length > 0) {
            setCurrentConversationId(remainingConversations[0].id);
          } else {
            setCurrentConversationId(null);
            setMessages([]);
          }
        }
      } else {
        // Type guard: only access error in the failure case
        console.error('Error deleting conversation:', !result.success ? result.error : 'Unknown error');
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  };
  
  // Select a conversation
  const handleSelectConversation = (conversationId: string) => {
    setCurrentConversationId(conversationId);
  };
  
  // Toggle sidebar
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };  

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim() || !user || !currentConversationId) return;
    
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      conversationId: currentConversationId,
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString(),
    };
    
    // Add user message to chat
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Save user message to database
      await addChatMessage({
        conversationId: currentConversationId,
        role: 'user',
        content: userMessage.content
      });
      
      // Get response from Amber using journal entries as context
      const response = await generateJournalChatResponse(userMessage.content);
      
      if (response.success && response.data) {
        const amberMessage: ChatMessage = {
          id: `amber-${Date.now()}`,
          conversationId: currentConversationId,
          role: 'assistant',
          content: response.data.response,
          timestamp: new Date().toISOString(),
          context: response.data.context,
        };
        
        // Add assistant message to chat
        setMessages((prev) => [...prev, amberMessage]);
        
        // Save assistant message to database
        await addChatMessage({
          conversationId: currentConversationId,
          role: 'assistant',
          content: amberMessage.content,
          context: amberMessage.context || undefined
        });
        
        // Update conversation list to reflect the new message
        loadConversations();
      } else {
        // Handle error
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          conversationId: currentConversationId,
          role: 'assistant',
          content: "I'm sorry, I couldn't process that request right now. Let's try a different question?",
          timestamp: new Date().toISOString(),
          isError: true,
        };
        
        setMessages((prev) => [...prev, errorMessage]);
        
        // Save error message to database
        await addChatMessage({
          conversationId: currentConversationId,
          role: 'assistant',
          content: errorMessage.content,
          isError: true
        });
      }
    } catch (error) {
      console.error('Error generating response:', error);
      
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        conversationId: currentConversationId,
        role: 'assistant',
        content: "I'm sorry, something went wrong. Let's try again?",
        timestamp: new Date().toISOString(),
        isError: true,
      };
      
      setMessages((prev) => [...prev, errorMessage]);
      
      // Save error message to database
      try {
        await addChatMessage({
          conversationId: currentConversationId,
          role: 'assistant',
          content: errorMessage.content,
          isError: true
        });
      } catch (saveError) {
        console.error('Error saving error message:', saveError);
      }
    } finally {
      setIsLoading(false);
      // Focus back on input after response
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  // Handle input changes with auto-resize
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    
    // Auto-resize textarea
    e.target.style.height = 'auto';
    e.target.style.height = `${Math.min(e.target.scrollHeight, 120)}px`;
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Show error message if there's an error
  if (error) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] w-full">
          <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded-md max-w-md">
            <h3 className="font-semibold mb-2">Something went wrong</h3>
            <p className="mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Refresh Page
            </Button>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="flex h-[calc(100vh-4rem)] w-full">
        {/* Sidebar */}
        <div 
          className={`border-r border-amber-200 bg-amber-50/70 backdrop-blur transition-all duration-300 flex flex-col ${
            isSidebarOpen ? 'w-64' : 'w-0 overflow-hidden'
          }`}
        >
          {/* New chat button */}
          <div className="p-3">
            <Button 
              onClick={handleCreateNewChat} 
              disabled={isCreatingConversation} 
              className="w-full bg-amber-500 hover:bg-amber-600 text-white flex gap-2 items-center justify-center py-5"
            >
              {isCreatingConversation ? (
                <LoadingSpinner size="sm" />
              ) : (
                <>
                  <PlusIcon size={16} />
                  <span>New Chat</span>
                </>
              )}
            </Button>
          </div>
          
          {/* Chat list */}
          <div className="flex-1 overflow-y-auto">
            {isLoadingConversations ? (
              <div className="flex items-center justify-center h-20">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-amber-800">Loading chats...</span>
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center text-amber-700 p-4">
                <p>No conversations yet</p>
                <p className="text-xs mt-1">Create a new chat to get started</p>
              </div>
            ) : (
              <div className="space-y-1 p-2">
                {conversations.map(conversation => (
                  <div 
                    key={conversation.id} 
                    className={`flex justify-between items-center p-2 rounded-md hover:bg-amber-100 cursor-pointer ${
                      currentConversationId === conversation.id ? 'bg-amber-100' : ''
                    }`}
                  >
                    <div 
                      className="flex items-center gap-2 flex-1 truncate"
                      onClick={() => handleSelectConversation(conversation.id)}
                    >
                      <MessageSquareIcon size={16} className="text-amber-600" />
                      <span className="truncate">{conversation.title}</span>
                    </div>
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteConversation(conversation.id);
                      }}
                      className="p-1 hover:text-red-500 transition-colors"
                      title="Delete conversation"
                    >
                      <Trash2Icon size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Main chat area */}
        <div className="flex-1 flex flex-col h-full">
          {/* Top bar with toggle */}
          <div className="p-3 border-b border-amber-200 flex items-center">
            <Button 
              onClick={toggleSidebar} 
              variant="ghost" 
              size="sm"
              className="mr-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            </Button>
            
            <h1 className="text-xl font-semibold text-amber-800">
              {currentConversationId && conversations.find(c => c.id === currentConversationId)?.title || "Talk to Amber"}
            </h1>
            
            {currentConversationId && (
              <Button
                onClick={handleCreateNewChat}
                variant="ghost"
                size="sm"
                className="ml-auto"
              >
                <PlusIcon size={16} />
              </Button>
            )}
          </div>
          
          <div className="flex flex-col flex-1 p-4 h-full overflow-hidden">
            {/* Error display */}
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start">
                  <div className="text-red-600 text-sm flex-1">
                    <strong>Error:</strong> {error}
                    {error.includes('Database tables missing') && (
                      <div className="mt-2 text-xs text-red-500">
                        <strong>To fix this:</strong>
                        <ol className="list-decimal list-inside mt-1 space-y-1">
                          <li>Start Docker Desktop</li>
                          <li>Run: <code className="bg-red-100 px-1 rounded">npx supabase start</code></li>
                          <li>Run: <code className="bg-red-100 px-1 rounded">npx supabase db reset</code></li>
                          <li>Click the retry button below</li>
                        </ol>
                        <Button
                          onClick={() => {
                            setError(null);
                            if (user) {
                              checkJournalChatTables().then(tableCheck => {
                                if (!tableCheck.conversationsTableExists || !tableCheck.messagesTableExists) {
                                  setError(`Database tables still missing. Please run the migration first.`);
                                  return;
                                }
                                loadConversations();
                              });
                            }
                          }}
                          size="sm"
                          className="mt-2 bg-red-600 hover:bg-red-700 text-white"
                        >
                          Retry Connection
                        </Button>
                      </div>
                    )}
                  </div>
                  <Button
                    onClick={() => setError(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-2 text-red-600 hover:text-red-800"
                  >
                    ×
                  </Button>
                </div>
              </div>
            )}

            {/* Chat message container */}
            <div className="flex-1 overflow-y-auto pb-4 space-y-4 mb-4">
              {messages.length === 0 && !isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-amber-700">
                    <MessageSquareIcon size={36} className="mx-auto mb-3 opacity-50" />
                    <h2 className="text-xl font-semibold">No messages yet</h2>
                    <p className="mt-1">Send a message to start chatting with Amber about your journal</p>
                  </div>
                </div>
              ) : (
                messages.map((message) => (
                  <div 
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div 
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.role === 'user' 
                          ? 'bg-amber-100 text-amber-900' 
                          : message.isError 
                            ? 'bg-red-100 text-red-800'
                            : 'bg-amber-500/90 text-white shadow-md animate-fadeIn'
                      }`}
                    >
                      <div className="prose prose-sm whitespace-pre-wrap">
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))
              )}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-amber-500/90 p-4 rounded-lg text-white shadow-md animate-pulse">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2">Amber is thinking...</span>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
            
            {/* Input form */}
            <form onSubmit={handleSubmit} className="pb-4">
              <div className="relative">
                <textarea
                  ref={inputRef}
                  value={input}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  placeholder={currentConversationId ? "Ask Amber about your journal entries..." : "Create a new chat to start"}
                  className="w-full p-4 pr-16 rounded-lg border-2 border-amber-300 focus:border-amber-500 focus:ring-2 focus:ring-amber-200 resize-none overflow-hidden min-h-[56px] max-h-[120px] bg-white/90 backdrop-blur-sm"
                  disabled={isLoading || !currentConversationId}
                  rows={1}
                />
                <button
                  type="submit"
                  disabled={isLoading || !input.trim() || !currentConversationId}
                  className="absolute right-2 bottom-2 p-2 rounded-full bg-amber-500 text-white hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Press Enter to send. Shift+Enter for a new line.
              </p>
            </form>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default JournalChat;

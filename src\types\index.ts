/**
 * Types Index
 * Central export point for all TypeScript interfaces and types
 */

// Journal types
export type {
  EmotionType,
  MoodScore,
  AIReflection,
  JournalEntry,
  CreateJournalEntryPayload,
  UpdateJournalEntryPayload,
  FormattedJournalEntry,
  JournalStats,
  JournalEntryFilters,
  JournalPaginationState,
  JournalPaginationOptions,
  JournalPaginationResult,
  JournalPaginationActions,
} from './journal';

// Chat types
export type {
  ChatRole,
  ChatMessage,
  JournalChatContext,
  JournalChatResponse,
} from './chat';

// Authentication types
export type {
  UserProfile,
  AuthContextType,
  SignUpFormData,
  SignInFormData,
  PasswordResetFormData,
  PasswordUpdateFormData,
  ProfileUpdateFormData,
  AuthErrorType,
  AuthError,
  AuthResponse,
  SessionState,
} from './auth';

// AI types
export type {
  AIReflectionInput,
  LLMMessage,
  GeminiAIResponse,
  LocalLLMResponse,
  AIResponse,
  AIReflectionResponse,
  AIServiceConfig,
  AIPromptTemplate,
  MockAIResponseTemplate,
  AIErrorType,
  AIError,
  AIServiceResponse,
  AIUsageStats,
  AIFeatureConfig,
  MemoryCategory,
  UserMemory,
  MemoryExtractionInput,
  MemoryExtractionResponse,
} from './ai';

// UI types
export type {
  BaseComponentProps,
  LoadingState,
  ErrorState,
  NavigationView,
  NavigationProps,
  ButtonVariant,
  ButtonSize,
  FieldValidation,
  FormState,
  ModalProps,
  ToastType,
  ToastNotification,
  PaginationState,
  SearchFilters,
  ThemeConfig,
  Breakpoint,
  AnimationState,
} from './ui';

// API types
export type {
  ApiResponse,
  ApiError,
  ApiMeta,
  PaginationMeta,
  RateLimitMeta,
  ApiRequestConfig,
  CacheConfig,
  ServiceOptions,
  QueryOptions,
  BulkOperationResult,
  FileUploadConfig,
  FileUploadResult,
  HealthCheckResponse,
} from './api';

// Database types
export type {
  SupabaseJournalEntry,
  SupabaseJournalEntryInsert,
  SupabaseJournalEntryUpdate,
  SupabaseProfile,
  SupabaseProfileInsert,
  SupabaseProfileUpdate,
  SupabaseReflectionConversation,
  SupabaseReflectionConversationInsert,
  SupabaseReflectionConversationUpdate,
  SupabaseConversationMessage,
  SupabaseConversationMessageInsert,
  SupabaseConversationMessageUpdate,
  DatabaseQueryResult,
  DatabaseOperationResult,
} from './database';

// Environment types
export type {
  Environment,
  EnvironmentVariables,
  EnvironmentConfig,
  EnvironmentValidationResult,
  EnvironmentVariableDefinition,
  ConfigurationErrorType,
  ConfigurationError,
} from './environment';

// Analytics types
export type {
  MoodAnalyticsData,
  MoodAnalyticsStats,
  MoodTrendData,
  EmotionDistributionData,
  DailyMoodData,
  MoodEmotionCorrelationData,
  MoodPatternData,
  AnalyticsTimeRange,
  AnalyticsFilters,
  ChartConfig,
  AnalyticsExportData,
  MoodInsight,
} from './analytics';

// Performance types
export type {
  PerformanceCacheConfig,
  QueryCacheConfig,
  PerformanceMetrics,
  PerformanceMonitoringConfig,
  CacheEntry,
  CacheStats,
  QueryKeyFactory,
  OptimisticUpdateConfig,
  BackgroundSyncConfig,
  PrefetchConfig,
  OptimizationStrategy,
  ComponentPerformanceConfig,
  PerformanceBudget,
  PerformanceAlert,
} from './performance';

// Settings types
export type {
  UserSettingsFormData,
  UserSettingsUpdatePayload,
  BulkDeleteConfirmation,
  BulkDeleteResult,
  SettingsLoadingStates,
  SettingsErrorStates,
  SettingsFormErrors,
  SettingsPageState,
  UserSettingsUpdateResponse,
  BulkDeleteResponse,
  SettingsFormField,
  SettingsAction,
} from './settings';

// Conversation types
export type {
  MessageSenderType,
  MessageType,
  AIMessageMetadata,
  ConversationMessage,
  ReflectionConversation,
  CreateMessageInput,
  CreateConversationInput,
  AIConversationResponse,
  AIConversationInput,
  ConversationState,
  ConversationActions,
  FormattedConversationMessage,
  ConversationPagination,
  ConversationAnalytics,
} from './conversation';

// Database converter functions
export {
  supabaseToJournalEntry,
  journalEntryToSupabaseInsert,
  journalEntryToSupabaseUpdate,
  supabaseToUserProfile,
  userProfileToSupabaseInsert,
  userProfileToSupabaseUpdate,
  supabaseToReflectionConversation,
  reflectionConversationToSupabaseInsert,
  supabaseToConversationMessage,
  conversationMessageToSupabaseInsert,
} from './database';

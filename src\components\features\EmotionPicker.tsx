import React from 'react';
import { Button } from '@/components/ui/button';
import { EmotionType } from '@/types';
import { useStableCallback } from '@/utils/performance.utils';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>rk<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>Rain
} from 'lucide-react';

interface EmotionOption {
  name: EmotionType;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
  hoverGradient: string;
  textColor: string;
  description: string;
}

const emotions: EmotionOption[] = [
  {
    name: 'grateful',
    icon: Heart,
    gradient: 'bg-gradient-to-br from-emerald-100 to-green-200',
    hoverGradient: 'hover:from-emerald-200 hover:to-green-300',
    textColor: 'text-emerald-700',
    description: 'Thankful and appreciative'
  },
  {
    name: 'joyful',
    icon: Smile,
    gradient: 'bg-gradient-to-br from-yellow-100 to-amber-200',
    hoverGradient: 'hover:from-yellow-200 hover:to-amber-300',
    textColor: 'text-amber-700',
    description: 'Happy and cheerful'
  },
  {
    name: 'calm',
    icon: Sun,
    gradient: 'bg-gradient-to-br from-sky-100 to-blue-200',
    hoverGradient: 'hover:from-sky-200 hover:to-blue-300',
    textColor: 'text-blue-700',
    description: 'Peaceful and relaxed'
  },
  {
    name: 'excited',
    icon: Sparkles,
    gradient: 'bg-gradient-to-br from-orange-100 to-amber-200',
    hoverGradient: 'hover:from-orange-200 hover:to-amber-300',
    textColor: 'text-orange-700',
    description: 'Energetic and enthusiastic'
  },
  {
    name: 'anxious',
    icon: CloudRain,
    gradient: 'bg-gradient-to-br from-purple-100 to-violet-200',
    hoverGradient: 'hover:from-purple-200 hover:to-violet-300',
    textColor: 'text-purple-700',
    description: 'Worried and nervous'
  },
  {
    name: 'sad',
    icon: Frown,
    gradient: 'bg-gradient-to-br from-indigo-100 to-blue-200',
    hoverGradient: 'hover:from-indigo-200 hover:to-blue-300',
    textColor: 'text-indigo-700',
    description: 'Down and melancholy'
  },
  {
    name: 'frustrated',
    icon: Angry,
    gradient: 'bg-gradient-to-br from-red-100 to-rose-200',
    hoverGradient: 'hover:from-red-200 hover:to-rose-300',
    textColor: 'text-red-700',
    description: 'Annoyed and irritated'
  },
  {
    name: 'neutral',
    icon: Meh,
    gradient: 'bg-gradient-to-br from-gray-100 to-slate-200',
    hoverGradient: 'hover:from-gray-200 hover:to-slate-300',
    textColor: 'text-gray-700',
    description: 'Balanced and even'
  },
  {
    name: 'hopeful',
    icon: Star,
    gradient: 'bg-gradient-to-br from-pink-100 to-rose-200',
    hoverGradient: 'hover:from-pink-200 hover:to-rose-300',
    textColor: 'text-pink-700',
    description: 'Optimistic and positive'
  },
  {
    name: 'overwhelmed',
    icon: Brain,
    gradient: 'bg-gradient-to-br from-slate-100 to-gray-200',
    hoverGradient: 'hover:from-slate-200 hover:to-gray-300',
    textColor: 'text-slate-700',
    description: 'Stressed and overloaded'
  },
];

interface EmotionPickerProps {
  selectedEmotion: EmotionType | '';
  onEmotionSelect: (emotion: EmotionType) => void;
}

const EmotionPickerComponent = ({ selectedEmotion, onEmotionSelect }: EmotionPickerProps) => {
  // Performance monitoring - temporarily disabled
  // useRenderCount('EmotionPicker');

  // Stable callback for emotion selection
  const handleEmotionSelect = useStableCallback(
    (emotion: EmotionType) => {
      onEmotionSelect(emotion);
    },
    [onEmotionSelect],
    'EmotionPicker.handleEmotionSelect'
  );

  // Memoize emotion buttons to prevent unnecessary re-renders
  const emotionButtons = React.useMemo(
    () =>
      emotions.map(emotion => {
        const IconComponent = emotion.icon;
        const isSelected = selectedEmotion === emotion.name;

        return (
          <Button
            key={emotion.name}
            type="button"
            variant="ghost"
            onClick={() => handleEmotionSelect(emotion.name)}
            className={`
              group relative h-auto p-4 flex flex-col items-center gap-3
              transition-all duration-300 ease-out
              border-2 rounded-xl backdrop-blur-sm
              ${
                isSelected
                  ? 'bg-gradient-to-br from-amber-400 to-orange-500 text-white border-amber-400 shadow-lg shadow-amber-200/50 scale-105'
                  : `${emotion.gradient} ${emotion.hoverGradient} ${emotion.textColor} border-transparent hover:border-amber-300/50 hover:shadow-md hover:scale-102`
              }
            `}
          >
            {/* Icon with subtle animation */}
            <div className={`
              p-2 rounded-full transition-all duration-300
              ${isSelected
                ? 'bg-white/20 shadow-inner'
                : 'bg-white/40 group-hover:bg-white/60'
              }
            `}>
              <IconComponent
                className={`
                  w-6 h-6 transition-all duration-300
                  ${isSelected
                    ? 'text-white drop-shadow-sm'
                    : `${emotion.textColor} group-hover:scale-110`
                  }
                `}
              />
            </div>

            {/* Emotion name */}
            <div className="text-center">
              <span className={`
                text-sm font-semibold capitalize transition-all duration-300
                ${isSelected
                  ? 'text-white drop-shadow-sm'
                  : `${emotion.textColor} group-hover:font-bold`
                }
              `}>
                {emotion.name}
              </span>

              {/* Description - only show on hover for unselected items */}
              <p className={`
                text-xs mt-1 transition-all duration-300 opacity-0 group-hover:opacity-70
                ${isSelected
                  ? 'text-white/90 opacity-90'
                  : `${emotion.textColor}`
                }
              `}>
                {emotion.description}
              </p>
            </div>

            {/* Selected indicator */}
            {isSelected && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full shadow-md animate-pulse" />
            )}
          </Button>
        );
      }),
    [selectedEmotion, handleEmotionSelect]
  );

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
      {emotionButtons}
    </div>
  );
};

// Memoized component
export const EmotionPicker = React.memo(EmotionPickerComponent, (prevProps, nextProps) => {
  return (
    prevProps.selectedEmotion === nextProps.selectedEmotion &&
    prevProps.onEmotionSelect === nextProps.onEmotionSelect
  );
});
